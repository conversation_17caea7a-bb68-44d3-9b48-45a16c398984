import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import api from '../../services/api';
import ModernLayout from '../../components/Layout/ModernLayout';
import { FiDownload, FiFileText, FiFilter, FiRefreshCw, FiFile } from 'react-icons/fi';
import * as XLSX from 'xlsx';

const ExportReadings = () => {
  const [filterParams, setFilterParams] = useState({
    meterId: '',
    customerId: '',
    readingSheet: '',
    status: '',
    startDate: '',
    endDate: ''
  });
  const [readings, setReadings] = useState([]);
  const [allReadings, setAllReadings] = useState([]);
  const [loading, setLoading] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  const { meterId, customerId, readingSheet, status, startDate, endDate } = filterParams;

  const onChange = (e) => {
    setFilterParams({ ...filterParams, [e.target.name]: e.target.value });
  };

  // Load all readings on component mount
  useEffect(() => {
    loadAllReadings();
  }, []);

  const loadAllReadings = async () => {
    setLoading(true);
    try {
      // Get all readings without pagination
      const res = await api.get('/api/readings?limit=10000');
      setAllReadings(res.data.data);
      setReadings(res.data.data);
      setTotalCount(res.data.count || res.data.data.length);
      setLoading(false);
    } catch (error) {
      toast.error('Échec du chargement des relevés');
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filteredReadings = [...allReadings];

    // Apply filters
    if (meterId) {
      filteredReadings = filteredReadings.filter(reading =>
        reading.meterId.toLowerCase().includes(meterId.toLowerCase())
      );
    }
    if (customerId) {
      filteredReadings = filteredReadings.filter(reading =>
        reading.customerId.toLowerCase().includes(customerId.toLowerCase())
      );
    }
    if (status) {
      filteredReadings = filteredReadings.filter(reading => reading.status === status);
    }
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      filteredReadings = filteredReadings.filter(reading => {
        const readingDate = new Date(reading.readingDate);
        return readingDate >= start && readingDate <= end;
      });
    }

    setReadings(filteredReadings);
  };

  const resetFilters = () => {
    setFilterParams({
      meterId: '',
      customerId: '',
      readingSheet: '',
      status: '',
      startDate: '',
      endDate: ''
    });
    setReadings(allReadings);
  };

  const exportToCSV = () => {
    setExporting(true);
    try {
      const csvData = readings.map(reading => ({
        'ID Compteur': reading.meterId,
        'ID Client': reading.customerId,
        'Nom Client': reading.customerName,
        'Valeur Relevé': reading.readingValue,
        'Date Relevé': new Date(reading.readingDate).toLocaleDateString('fr-FR'),
        'Statut': reading.status,
        'Fiche': reading.readingSheet ? reading.readingSheet.sheetNumber : 'N/A',
        'Notes': reading.notes || '',
        'Mode Validation': reading.validationMode,
        'Créé le': new Date(reading.createdAt).toLocaleDateString('fr-FR')
      }));

      const ws = XLSX.utils.json_to_sheet(csvData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Relevés');

      const fileName = `releves_${new Date().toISOString().split('T')[0]}.csv`;
      XLSX.writeFile(wb, fileName, { bookType: 'csv' });

      toast.success('Export CSV réussi');
    } catch (error) {
      toast.error('Erreur lors de l\'export CSV');
    } finally {
      setExporting(false);
    }
  };

  const exportToExcel = () => {
    setExporting(true);
    try {
      const excelData = readings.map(reading => ({
        'ID Compteur': reading.meterId,
        'ID Client': reading.customerId,
        'Nom Client': reading.customerName,
        'Valeur Relevé': reading.readingValue,
        'Date Relevé': new Date(reading.readingDate).toLocaleDateString('fr-FR'),
        'Statut': reading.status,
        'Fiche': reading.readingSheet ? reading.readingSheet.sheetNumber : 'N/A',
        'Notes': reading.notes || '',
        'Mode Validation': reading.validationMode,
        'Créé le': new Date(reading.createdAt).toLocaleDateString('fr-FR')
      }));

      const ws = XLSX.utils.json_to_sheet(excelData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Relevés');

      const fileName = `releves_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(wb, fileName);

      toast.success('Export Excel réussi');
    } catch (error) {
      toast.error('Erreur lors de l\'export Excel');
    } finally {
      setExporting(false);
    }
  };

  return (
    <ModernLayout>
      <div className="p-6">
        <div className="space-y-4">
          <div className="bg-white p-5 border-b border-gray-200 shadow-sm rounded-t-lg">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <h2 className="text-xl font-medium text-gray-800 flex items-center gap-2">
                  <FiDownload className="text-primary-600" /> Export des Relevés
                </h2>
                <p className="text-sm text-gray-500 mt-1">Exportez tous les relevés sauvegardés en Excel ou CSV</p>
              </div>
              <div className="flex gap-2 mt-4 md:mt-0">
                <button
                  onClick={exportToCSV}
                  disabled={exporting || readings.length === 0}
                  className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <FiFile className="h-4 w-4" />
                  {exporting ? 'Export...' : 'Export CSV'}
                </button>
                <button
                  onClick={exportToExcel}
                  disabled={exporting || readings.length === 0}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <FiFileText className="h-4 w-4" />
                  {exporting ? 'Export...' : 'Export Excel'}
                </button>
                <button
                  onClick={loadAllReadings}
                  disabled={loading}
                  className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <FiRefreshCw className="h-4 w-4" />
                  {loading ? 'Chargement...' : 'Actualiser'}
                </button>
              </div>
            </div>
          </div>
          <div className="bg-white border border-gray-200 shadow-sm rounded-b-lg">
            <div className="p-5">
              <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center gap-2">
                <FiFilter className="text-primary-600" />
                Filtres (Optionnel)
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                <div>
                  <label htmlFor="meterId" className="block text-sm font-medium text-gray-700 mb-1">ID Compteur</label>
                  <input type="text" id="meterId" name="meterId" value={meterId} onChange={onChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" />
                </div>
                <div>
                  <label htmlFor="customerId" className="block text-sm font-medium text-gray-700 mb-1">ID Client</label>
                  <input type="text" id="customerId" name="customerId" value={customerId} onChange={onChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" />
                </div>
                <div>
                  <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                  <select id="status" name="status" value={status} onChange={onChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                    <option value="">Tous les statuts</option>
                    <option value="pending">En attente</option>
                    <option value="validated">Validé</option>
                    <option value="error">Erreur</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">Date de début</label>
                  <input type="date" id="startDate" name="startDate" value={startDate} onChange={onChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" />
                </div>
                <div>
                  <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">Date de fin</label>
                  <input type="date" id="endDate" name="endDate" value={endDate} onChange={onChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" />
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-5">
                <button
                  type="button"
                  onClick={resetFilters}
                  className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Réinitialiser
                </button>
                <button
                  type="button"
                  onClick={applyFilters}
                  className="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Appliquer Filtres
                </button>
              </div>
            </div>
          </div>
          <div className="bg-white border border-gray-200 shadow-sm rounded-lg p-5 mt-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Relevés Sauvegardés ({readings.length} sur {totalCount})</h3>
              {loading && (
                <div className="flex items-center text-gray-500">
                  <FiRefreshCw className="animate-spin mr-2" />
                  Chargement...
                </div>
              )}
            </div>
            {readings.length === 0 ? (
              <div className="text-center py-8">
                <FiFileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p className="text-gray-500">
                  {loading ? 'Chargement des relevés...' : 'Aucun relevé trouvé.'}
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID Compteur</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valeur</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fiche</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {readings.map(reading => (
                      <tr key={reading._id} className="hover:bg-gray-50">
                        <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">{reading.meterId}</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                          <div>
                            <div className="font-medium">{reading.customerName}</div>
                            <div className="text-gray-500">({reading.customerId})</div>
                          </div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{reading.readingValue}</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{new Date(reading.readingDate).toLocaleDateString('fr-FR')}</td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            reading.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            reading.status === 'validated' ? 'bg-green-100 text-green-800' :
                            reading.status === 'error' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {reading.status === 'pending' ? 'En attente' :
                             reading.status === 'validated' ? 'Validé' :
                             reading.status === 'error' ? 'Erreur' : reading.status}
                          </span>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                          {reading.readingSheet ? reading.readingSheet.sheetNumber : 'N/A'}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900 max-w-xs truncate">
                          {reading.notes || '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>
    </ModernLayout>
  );
};

export default ExportReadings;