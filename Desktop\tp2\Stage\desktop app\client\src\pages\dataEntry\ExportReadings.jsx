import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import api from '../../services/api';
import { useNavigate } from 'react-router-dom';
import ModernLayout from '../../components/Layout/ModernLayout';
import { FiDownload, FiFileText, FiRefreshCw, FiFile, FiEdit, FiSearch } from 'react-icons/fi';
import * as XLSX from 'xlsx';

const ExportReadings = () => {
  const [readings, setReadings] = useState([]);
  const [loading, setLoading] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredReadings, setFilteredReadings] = useState([]);
  const navigate = useNavigate();

  // Load all readings on component mount
  useEffect(() => {
    loadAllReadings();
  }, []);
  
  // Filter readings based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredReadings(readings);
      return;
    }
    
    const lowercasedSearch = searchTerm.toLowerCase();
    const filtered = readings.filter(reading => 
      (reading.reference && reading.reference.toLowerCase().includes(lowercasedSearch)) ||
      (reading.client && reading.client.toLowerCase().includes(lowercasedSearch)) ||
      (reading.meterId && reading.meterId.toString().toLowerCase().includes(lowercasedSearch))
    );
    
    setFilteredReadings(filtered);
  }, [searchTerm, readings]);

  const loadAllReadings = async () => {
    setLoading(true);
    try {
      // Get all readings without pagination
      const res = await api.get('/api/readings?limit=10000');
      const data = res.data.data || [];
      setReadings(data);
      setFilteredReadings(data);
      setLoading(false);
    } catch (error) {
      toast.error('Échec du chargement des relevés');
      setLoading(false);
    }
  };
  
  const handleEdit = (readingId) => {
    // Navigate to edit page with the reading ID
    navigate(`/readings/edit/${readingId}`);
  };

  const exportToCSV = () => {
    setExporting(true);
    try {
      const csvData = readings.map(reading => ({
        'Référence': reading.reference,
        'Client': reading.client,
        'Code d\'Agence': reading.codeAgence || '',
        'Groupe': reading.groupe,
        'Tournée': reading.tournee,
        'Circuit': reading.circuit,
        'N° de Compteur': reading.meterId,
        'Ancien Index': reading.ancienIndex,
        'Nouvel Index': reading.newIndex,
        'Tarif': reading.tarif || '',
        'CAD': reading.cad || '',
        'Adresse': reading.adress || '',
        'Représentant Commercial': reading.salesRep ? `${reading.salesRep.name} (${reading.salesRep.reference})` : '',
        'Validé Par': reading.validatedBy ? `${reading.validatedBy.name} (${reading.validatedBy.reference})` : '',
        'Date Relevé': new Date(reading.readingDate).toLocaleDateString('fr-FR'),
        'Statut': reading.status,
        'Fiche': reading.readingSheet ? reading.readingSheet.sheetNumber : 'N/A',
        'Notes': reading.notes || '',
        'Mode Validation': reading.validationMode,
        'Créé le': new Date(reading.createdAt).toLocaleDateString('fr-FR')
      }));

      const ws = XLSX.utils.json_to_sheet(csvData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Relevés');

      const fileName = `releves_${new Date().toISOString().split('T')[0]}.csv`;
      XLSX.writeFile(wb, fileName, { bookType: 'csv' });

      toast.success('Export CSV réussi');
    } catch (error) {
      toast.error('Erreur lors de l\'export CSV');
    } finally {
      setExporting(false);
    }
  };

  const exportToExcel = () => {
    setExporting(true);
    try {
      const excelData = readings.map(reading => ({
        'Référence': reading.reference,
        'Client': reading.client,
        'Code d\'Agence': reading.codeAgence || '',
        'Groupe': reading.groupe,
        'Tournée': reading.tournee,
        'Circuit': reading.circuit,
        'N° de Compteur': reading.meterId,
        'Ancien Index': reading.ancienIndex,
        'Nouvel Index': reading.newIndex,
        'Tarif': reading.tarif || '',
        'CAD': reading.cad || '',
        'Adresse': reading.adress || '',
        'Représentant Commercial': reading.salesRep ? `${reading.salesRep.name} (${reading.salesRep.reference})` : '',
        'Validé Par': reading.validatedBy ? `${reading.validatedBy.name} (${reading.validatedBy.reference})` : '',
        'Date Relevé': new Date(reading.readingDate).toLocaleDateString('fr-FR'),
        'Statut': reading.status,
        'Fiche': reading.readingSheet ? reading.readingSheet.sheetNumber : 'N/A',
        'Notes': reading.notes || '',
        'Mode Validation': reading.validationMode,
        'Créé le': new Date(reading.createdAt).toLocaleDateString('fr-FR')
      }));

      const ws = XLSX.utils.json_to_sheet(excelData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Relevés');

      const fileName = `releves_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(wb, fileName);

      toast.success('Export Excel réussi');
    } catch (error) {
      toast.error('Erreur lors de l\'export Excel');
    } finally {
      setExporting(false);
    }
  };

  return (
    <ModernLayout>
      <div className="space-y-4">
        {/* Page Header - Formal Style */}
        <div className="bg-white p-5 border-b border-gray-200 shadow-sm">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h2 className="text-xl font-medium text-gray-800 flex items-center gap-2">
                <FiDownload className="text-primary-600" /> Export des Relevés
              </h2>
              <p className="text-sm text-gray-500 mt-1">Exportez tous les relevés sauvegardés en Excel ou CSV</p>
            </div>
            <div className="flex gap-2 mt-4 md:mt-0">
              <button
                onClick={exportToCSV}
                disabled={exporting || readings.length === 0}
                className="inline-flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FiFile className="h-4 w-4" />
                {exporting ? 'Export...' : 'Export CSV'}
              </button>
              <button
                onClick={exportToExcel}
                disabled={exporting || readings.length === 0}
                className="inline-flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FiFileText className="h-4 w-4" />
                {exporting ? 'Export...' : 'Export Excel'}
              </button>
              <button
                onClick={loadAllReadings}
                disabled={loading}
                className="inline-flex items-center gap-2 px-3 py-1.5 bg-white border border-gray-300 text-gray-700 text-sm font-medium rounded hover:bg-gray-50 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FiRefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                {loading ? 'Chargement...' : 'Actualiser'}
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="bg-white border border-gray-200 shadow-sm p-5">
          <div className="flex flex-col md:flex-row justify-between items-center mb-4 gap-3">
            <h3 className="text-lg font-semibold">Relevés Sauvegardés <span className="text-primary-600">({filteredReadings.length})</span></h3>
            
            <div className="relative w-full md:w-auto">
              <div className="flex items-center">
                <div className="relative flex-grow">
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Rechercher par référence, client, ou compteur..."
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded w-full md:w-64"
                  />
                  <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                </div>
                {searchTerm && (
                  <button
                    onClick={() => setSearchTerm('')}
                    className="ml-2 text-gray-500 hover:text-gray-700"
                    title="Effacer la recherche"
                  >
                    &times;
                  </button>
                )}
              </div>
            </div>
            
            {loading && (
              <div className="flex items-center text-gray-500">
                <FiRefreshCw className="animate-spin mr-2" />
                Chargement...
              </div>
            )}
          </div>
          
          {readings.length === 0 ? (
            <div className="text-center py-12">
              <FiFileText className="mx-auto h-16 w-16 text-gray-300 mb-4" />
              <p className="text-gray-500 text-lg">
                {loading ? 'Chargement des relevés...' : 'Aucun relevé trouvé.'}
              </p>
              <p className="text-gray-400 text-sm mt-2">
                {!loading && 'Les relevés apparaîtront ici une fois chargés.'}
              </p>
            </div>
          ) : filteredReadings.length === 0 ? (
            <div className="text-center py-12">
              <FiSearch className="mx-auto h-16 w-16 text-gray-300 mb-4" />
              <p className="text-gray-500 text-lg">
                Aucun résultat pour "{searchTerm}"
              </p>
              <p className="text-gray-400 text-sm mt-2">
                Essayez avec d'autres termes de recherche
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Référence</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Groupe/Tournée/Circuit</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">N° Compteur</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ancien Index</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nouvel Index</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tarif</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Validé Par</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredReadings.map(reading => (
                    <tr key={reading._id} className="hover:bg-gray-50 transition-colors duration-150">
                      <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">{reading.reference}</td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          <div className="font-medium">{reading.client}</div>
                          {reading.codeAgence && <div className="text-gray-500 text-xs">Code: {reading.codeAgence}</div>}
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                        <div className="text-xs">
                          <div>G{reading.groupe} - T{reading.tournee}</div>
                          <div className="text-gray-500">Circuit: {reading.circuit}</div>
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{reading.meterId}</td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{reading.ancienIndex}</td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 font-medium">{reading.newIndex}</td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{reading.tarif || '-'}</td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                        {reading.validatedBy ? (
                          <div className="text-xs">
                            <div className="font-medium">{reading.validatedBy.name}</div>
                            <div className="text-gray-500">{reading.validatedBy.reference}</div>
                          </div>
                        ) : '-'}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{new Date(reading.readingDate).toLocaleDateString('fr-FR')}</td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                          reading.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          reading.status === 'validated' || reading.status === 'validé' || reading.status === 'valide' ? 'bg-primary-100 text-primary-800' :
                          reading.status === 'error' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {reading.status === 'pending' ? 'En attente' :
                           reading.status === 'validated' || reading.status === 'validé' || reading.status === 'valide' ? 'Validé' :
                           reading.status === 'error' ? 'Erreur' : reading.status}
                        </span>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                        <button
                          onClick={() => handleEdit(reading._id)}
                          className="inline-flex items-center p-1.5 text-primary-600 hover:text-primary-800 hover:bg-primary-50 rounded transition-colors"
                          title="Modifier ce relevé"
                        >
                          <FiEdit className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </ModernLayout>
  );
};

export default ExportReadings;