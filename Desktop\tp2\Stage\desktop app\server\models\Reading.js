const mongoose = require('mongoose');

const ReadingSchema = new mongoose.Schema({
  // Basic reading information
  reference: {
    type: String,
    required: [true, 'Please add a reference'],
    trim: true
  },
  client: {
    type: String,
    required: [true, 'Please add a client name']
  },
  codeAgence: {
    type: String,
    trim: true
  },
  groupe: {
    type: String,
    required: [true, 'Please add a groupe']
  },
  tournee: {
    type: String,
    required: [true, 'Please add a tournee']
  },
  circuit: {
    type: String,
    required: [true, 'Please add a circuit']
  },
  meterId: {
    type: String,
    required: [true, 'Please add a meter ID'],
    trim: true
  },
  // Reading values
  ancienIndex: {
    type: Number,
    required: [true, 'Please add ancien index']
  },
  newIndex: {
    type: Number,
    required: [true, 'Please add new index']
  },
  // Additional information
  tarif: {
    type: String
  },
  cad: {
    type: String
  },
  adress: {
    type: String
  },
  // Staff information
  salesRep: {
    name: String,
    reference: String
  },
  validatedBy: {
    name: String,
    reference: String
  },
  // System fields
  readingDate: {
    type: Date,
    default: Date.now
  },
  readingSheet: {
    type: mongoose.Schema.ObjectId,
    ref: 'ReadingSheet',
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'validated', 'error'],
    default: 'pending'
  },
  validationMode: {
    type: String,
    enum: ['online', 'offline'],
    default: 'online'
  },
  notes: {
    type: String
  },
  createdBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('Reading', ReadingSchema);
