import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './context/AuthContext';
import { FiLoader } from 'react-icons/fi';

// Shared Pages
import Dashboard from './pages/Dashboard';
import Login from './pages/Login';
import ForgotPassword from './pages/ForgotPassword';
import Settings from './pages/Settings';

// Admin Pages
import UserManagement from './pages/admin/UserManagement';
import AddUser from './pages/admin/AddUser';
import UpdateUser from './pages/admin/UpdateUser';
import UserDetails from './pages/admin/UserDetails';

// Reading Sheets Pages
import ReadingSheets from './pages/dataEntry/ReadingSheets';
import AssignReadingSheets from './pages/dataEntry/AssignReadingSheets';
import ReceiveReadingSheets from './pages/dataEntry/ReceiveReadingSheets';

// Data Entry Pages
import ExportReadings from './pages/dataEntry/ExportReadings';
import ExportReports from './pages/dataEntry/ExportReports';

// Error Pages
import NotFound from './pages/NotFound';

// Protected Route Component
const ProtectedRoute = ({ children, allowedRoles }) => {
  const { user, isAuthenticated, loading } = useAuth();

  console.log('ProtectedRoute:', {
    isAuthenticated,
    loading,
    user: user ? { id: user.id, name: user.name, role: user.role } : null,
    allowedRoles
  });

  if (loading) {
    console.log('Auth is still loading, showing loading screen');
    return (
      <div className="flex justify-center items-center min-h-screen bg-sonelgaz-gray-50">
        <div className="text-center">
          <FiLoader className="animate-spin h-10 w-10 text-primary-500 mx-auto mb-4" />
          <p className="text-sonelgaz-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    console.log('User not authenticated, redirecting to login');
    return <Navigate to="/login" />;
  }

  if (allowedRoles && (!user || !allowedRoles.includes(user.role))) {
    console.log(`Role check failed: User role ${user?.role} not in allowed roles:`, allowedRoles);
    return <Navigate to="/" />;
  }

  console.log('Protected route access granted');

  // Support both render prop pattern and direct children
  if (typeof children === 'function') {
    return children({ user, isAuthenticated });
  }

  return children;
};

function App() {
  return (
    <Routes>
      {/* Public Routes */}
      <Route path="/login" element={<Login />} />
      <Route path="/forgot-password" element={<ForgotPassword />} />

      {/* Protected Routes */}
      <Route path="/" element={
        <ProtectedRoute>
          {({ user }) => {
            return user?.role === 'admin'
              ? <Navigate to="/users" />
              : <Navigate to="/dashboard" />;
          }}
        </ProtectedRoute>
      } />
      <Route path="/dashboard" element={
        <ProtectedRoute allowedRoles={['dataEntry']}>
          <Dashboard />
        </ProtectedRoute>
      } />
      <Route path="/settings" element={<ProtectedRoute><Settings /></ProtectedRoute>} />

      {/* Admin Routes */}
      <Route path="/users" element={<ProtectedRoute allowedRoles={['admin']}><UserManagement /></ProtectedRoute>} />
      <Route path="/add-user" element={<ProtectedRoute allowedRoles={['admin']}><AddUser /></ProtectedRoute>} />
      <Route path="/users/update/:id" element={<ProtectedRoute allowedRoles={['admin']}><UpdateUser /></ProtectedRoute>} />
      <Route path="/users/details/:id" element={<ProtectedRoute allowedRoles={['admin']}><UserDetails /></ProtectedRoute>} />

      {/* Reading Sheets Routes - Data Entry Only */}
      <Route path="/reading-sheets" element={<ProtectedRoute allowedRoles={['dataEntry']}><ReadingSheets /></ProtectedRoute>} />
      <Route path="/reading-sheets/assign" element={<ProtectedRoute allowedRoles={['dataEntry']}><AssignReadingSheets /></ProtectedRoute>} />
      <Route path="/reading-sheets/receive" element={<ProtectedRoute allowedRoles={['dataEntry']}><ReceiveReadingSheets /></ProtectedRoute>} />

      {/* Data Entry Staff Routes - Readings */}
      <Route path="/manage-readings" element={<ProtectedRoute allowedRoles={['dataEntry']}><ExportReadings /></ProtectedRoute>} />
      <Route path="/readings/export" element={<ProtectedRoute allowedRoles={['dataEntry']}><ExportReports /></ProtectedRoute>} />

      {/* 404 Route */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

export default App;
