import { NavLink } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import {
  <PERSON>a<PERSON><PERSON>s,
  FaFileAlt,
  FaSearch,
  FaClipboardList,
  FaUserPlus,
  FaKey,
  FaTachometerAlt
} from 'react-icons/fa';

const Sidebar = () => {
  const { user } = useAuth();
  const isAdmin = user?.role === 'admin';
  const isDataEntry = user?.role === 'dataEntry';

  return (
    <div className="fixed w-64 h-screen bg-secondary-800 text-white overflow-y-auto">
      <div className="p-5 text-center border-b border-secondary-700">
        <h3 className="text-xl font-bold text-white">Meter Reading System</h3>
      </div>
      <div className="py-4">
        {/* Shared Menu Items */}
        {isAdmin && (
          <NavLink
            to="/"
            className={({ isActive }) =>
              `flex items-center px-5 py-3 text-gray-300 hover:bg-secondary-700 hover:text-white transition-colors duration-200 ${isActive ? 'bg-primary-700 text-white' : ''}`
            }
          >
            <FaTachometerAlt className="mr-3" /> Dashboard
          </NavLink>
        )}
        {isAdmin && (
          <NavLink
            to="/change-password"
            className={({ isActive }) =>
              `flex items-center px-5 py-3 text-gray-300 hover:bg-secondary-700 hover:text-white transition-colors duration-200 ${isActive ? 'bg-primary-700 text-white' : ''}`
            }
          >
            <FaKey className="mr-3" /> Change Password
          </NavLink>
        )}

        {/* Admin Menu Items */}
        {isAdmin && (
          <div className="mt-6">
            <h4 className="px-5 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
              User Management
            </h4>
            <NavLink
              to="/users"
              className={({ isActive }) =>
                `flex items-center px-5 py-3 text-gray-300 hover:bg-secondary-700 hover:text-white transition-colors duration-200 ${isActive ? 'bg-primary-700 text-white' : ''}`
              }
            >
              <FaUsers className="mr-3" /> Manage Users
            </NavLink>
            <NavLink
              to="/users/register"
              className={({ isActive }) =>
                `flex items-center px-5 py-3 text-gray-300 hover:bg-secondary-700 hover:text-white transition-colors duration-200 ${isActive ? 'bg-primary-700 text-white' : ''}`
              }
            >
              <FaUserPlus className="mr-3" /> Register User
            </NavLink>
          </div>
        )}

        {/* Data Entry Staff Menu Items */}
        {isDataEntry && (
          <>
            <div className="mt-6">
              <h4 className="px-5 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                Opérations
              </h4>
              <NavLink
                to="/"
                className={({ isActive }) =>
                  `flex items-center px-5 py-3 text-gray-300 hover:bg-secondary-700 hover:text-white transition-colors duration-200 ${isActive ? 'bg-primary-700 text-white' : ''}`
                }
              >
                <FaTachometerAlt className="mr-3" /> Dashboard Overview
              </NavLink>
              <NavLink
                to="/data-entry/assign"
                className={({ isActive }) =>
                  `flex items-center px-5 py-3 text-gray-300 hover:bg-secondary-700 hover:text-white transition-colors duration-200 ${isActive ? 'bg-primary-700 text-white' : ''}`
                }
              >
                <FaClipboardList className="mr-3" /> Assign Reading Sheets
              </NavLink>
              <NavLink
                to="/data-entry/receive"
                className={({ isActive }) =>
                  `flex items-center px-5 py-3 text-gray-300 hover:bg-secondary-700 hover:text-white transition-colors duration-200 ${isActive ? 'bg-primary-700 text-white' : ''}`
                }
              >
                <FaClipboardList className="mr-3" /> Receive Reading Sheets
              </NavLink>
              <NavLink
                to="/data-entry/search"
                className={({ isActive }) =>
                  `flex items-center px-5 py-3 text-gray-300 hover:bg-secondary-700 hover:text-white transition-colors duration-200 ${isActive ? 'bg-primary-700 text-white' : ''}`
                }
              >
                <FaSearch className="mr-3" /> Search Readings
              </NavLink>
            </div>
            <div className="mt-6 border-t border-secondary-700 pt-4">
              <h4 className="px-5 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                Compte
              </h4>
              <NavLink
                to="/settings"
                className={({ isActive }) =>
                  `flex items-center px-5 py-3 text-gray-300 hover:bg-secondary-700 hover:text-white transition-colors duration-200 ${isActive ? 'bg-primary-700 text-white' : ''}`
                }
              >
                <FaUsers className="mr-3" /> Settings
              </NavLink>
              <NavLink
                to="/change-password"
                className={({ isActive }) =>
                  `flex items-center px-5 py-3 text-gray-300 hover:bg-secondary-700 hover:text-white transition-colors duration-200 ${isActive ? 'bg-primary-700 text-white' : ''}`
                }
              >
                <FaKey className="mr-3" /> Change Password
              </NavLink>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
